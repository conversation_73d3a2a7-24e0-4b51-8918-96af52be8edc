#!/usr/bin/env python3
"""
Setup script for Athlix Chat Application
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    print("🔍 Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} is compatible")
    return True

def install_dependencies():
    """Install Python dependencies"""
    if not run_command("pip install -r requirements.txt", "Installing dependencies"):
        return False
    return True

def setup_environment():
    """Setup environment configuration"""
    print("🔄 Setting up environment configuration...")
    
    env_file = Path(".env")
    env_template = Path(".env.template")
    
    if not env_file.exists() and env_template.exists():
        shutil.copy(env_template, env_file)
        print("✅ Created .env file from template")
        print("⚠️  Please edit .env file with your configuration")
        return True
    elif env_file.exists():
        print("✅ .env file already exists")
        return True
    else:
        print("❌ .env.template not found")
        return False

def initialize_database():
    """Initialize the database"""
    if not run_command("python init_db.py", "Initializing database"):
        return False
    return True

def create_uploads_directory():
    """Create uploads directory for file attachments"""
    print("🔄 Creating uploads directory...")
    
    uploads_dir = Path("uploads")
    uploads_dir.mkdir(exist_ok=True)
    
    print("✅ Uploads directory created")
    return True

def run_tests():
    """Run basic API tests"""
    print("🔄 Running basic tests...")
    
    # First, try to start the server in the background
    print("Starting server for testing...")
    
    try:
        # Start server in background
        server_process = subprocess.Popen(
            ["python", "-m", "uvicorn", "main:app", "--port", "8000"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Wait a moment for server to start
        import time
        time.sleep(3)
        
        # Run tests
        test_result = run_command("python test_api.py", "Running API tests")
        
        # Stop server
        server_process.terminate()
        server_process.wait()
        
        return test_result
        
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 Athlix Chat Application Setup")
    print("=" * 50)
    
    steps = [
        ("Python Version Check", check_python_version),
        ("Install Dependencies", install_dependencies),
        ("Setup Environment", setup_environment),
        ("Create Uploads Directory", create_uploads_directory),
        ("Initialize Database", initialize_database),
    ]
    
    # Run setup steps
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}")
        print("-" * 30)
        
        if not step_func():
            print(f"\n❌ Setup failed at: {step_name}")
            print("Please fix the error and run setup again.")
            return False
        
        print()
    
    # Ask about running tests
    print("=" * 50)
    run_test = input("Do you want to run basic API tests? (y/N): ").lower().strip()
    
    if run_test in ['y', 'yes']:
        print("\n📋 Running Tests")
        print("-" * 30)
        run_tests()
    
    # Final instructions
    print("\n" + "=" * 50)
    print("🎉 Setup Complete!")
    print("=" * 50)
    
    print("\nNext steps:")
    print("1. Edit .env file with your configuration:")
    print("   - Set SECRET_KEY to a secure random value")
    print("   - Add your OpenAI API key (gpt_api)")
    print("   - Configure email settings (optional)")
    
    print("\n2. Start the application:")
    print("   python main.py")
    print("   # or")
    print("   uvicorn main:app --reload")
    
    print("\n3. Access the application:")
    print("   - API Docs: http://localhost:8000/docs")
    print("   - Health Check: http://localhost:8000/health")
    
    print("\n4. Test the API:")
    print("   python test_api.py")
    
    print("\n📚 For more information, see README.md")

if __name__ == "__main__":
    main()
