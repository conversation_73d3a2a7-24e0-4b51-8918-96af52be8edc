#!/usr/bin/env python3
"""
Database initialization script for Athlix Chat Application
"""

import os
import sys
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

# Import models after setting up the path
from main import Base, User, EmailVerification, JwtToken, Chat, Message, MessageAttachment, ChatParticipant, UserSession

def init_database():
    """Initialize the database with all tables"""
    
    # Get database URL from environment
    database_url = os.getenv("DATABASE_URL", "sqlite:///./athlix.db")
    
    print(f"Initializing database: {database_url}")
    
    # Create engine
    engine = create_engine(
        database_url, 
        connect_args={"check_same_thread": False} if "sqlite" in database_url else {}
    )
    
    # Create all tables
    try:
        Base.metadata.create_all(bind=engine)
        print("✅ Database tables created successfully!")
        
        # Print created tables
        print("\nCreated tables:")
        for table_name in Base.metadata.tables.keys():
            print(f"  - {table_name}")
            
    except Exception as e:
        print(f"❌ Error creating database tables: {e}")
        return False
    
    return True

def create_sample_data():
    """Create sample data for testing (optional)"""
    
    database_url = os.getenv("DATABASE_URL", "sqlite:///./athlix.db")
    engine = create_engine(
        database_url, 
        connect_args={"check_same_thread": False} if "sqlite" in database_url else {}
    )
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Check if sample data already exists
        existing_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if existing_user:
            print("Sample data already exists. Skipping...")
            return
        
        from passlib.context import CryptContext
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        # Create a demo user
        demo_user = User(
            email="<EMAIL>",
            first_name="Demo",
            last_name="User",
            password_hash=pwd_context.hash("demo123"),
            email_verified=True,
            status="active"
        )
        
        db.add(demo_user)
        db.commit()
        db.refresh(demo_user)
        
        # Create a sample chat
        sample_chat = Chat(
            user_id=demo_user.user_id,
            chat_title="Welcome Chat",
            chat_type="personal"
        )
        
        db.add(sample_chat)
        db.commit()
        db.refresh(sample_chat)
        
        # Add user as participant
        participant = ChatParticipant(
            chat_id=sample_chat.chat_id,
            user_id=demo_user.user_id,
            role="owner"
        )
        
        db.add(participant)
        
        # Create a welcome message
        welcome_message = Message(
            chat_id=sample_chat.chat_id,
            user_id=demo_user.user_id,
            message_content="Welcome to Athlix! This is your first chat.",
            message_type="text",
            sender_type="system"
        )
        
        db.add(welcome_message)
        db.commit()
        
        print("✅ Sample data created successfully!")
        print("Demo user credentials:")
        print("  Email: <EMAIL>")
        print("  Password: demo123")
        
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 Athlix Database Initialization")
    print("=" * 40)
    
    # Initialize database
    if init_database():
        print("\n" + "=" * 40)
        
        # Ask if user wants to create sample data
        create_sample = input("Do you want to create sample data for testing? (y/N): ").lower().strip()
        if create_sample in ['y', 'yes']:
            create_sample_data()
    
    print("\n🎉 Database initialization complete!")
    print("\nNext steps:")
    print("1. Copy .env.template to .env and configure your settings")
    print("2. Run: python main.py or uvicorn main:app --reload")
    print("3. Visit: http://localhost:8000/docs for API documentation")
