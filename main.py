from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException, Form, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr
from typing import List, Dict, Optional, Any, AsyncGenerator
import base64
import json
import uuid
from datetime import datetime, timedelta
import os
from openai import OpenAI
from dotenv import load_dotenv
import io
from PIL import Image
import asyncio
from sqlalchemy import create_engine, Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session, relationship
from sqlalchemy.sql import func
from passlib.context import CryptContext
from jose import JWTError, jwt
import secrets
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import hashlib
from pathlib import Path

load_dotenv()

# Database setup
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./athlix.db")
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False} if "sqlite" in DATABASE_URL else {})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Security setup
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBearer()

# Configuration
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-here")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
EMAIL_HOST = os.getenv("EMAIL_HOST", "smtp.gmail.com")
EMAIL_PORT = int(os.getenv("EMAIL_PORT", "587"))
EMAIL_USER = os.getenv("EMAIL_USER", "")
EMAIL_PASSWORD = os.getenv("EMAIL_PASSWORD", "")

# Database Models
class User(Base):
    __tablename__ = "users"

    user_id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)
    password_hash = Column(String, nullable=False)
    email_verified = Column(Boolean, default=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_login = Column(DateTime)
    status = Column(String, default="active")

    # Relationships
    email_verifications = relationship("EmailVerification", back_populates="user")
    jwt_tokens = relationship("JwtToken", back_populates="user")
    chats = relationship("Chat", back_populates="user")
    messages = relationship("Message", back_populates="user")
    chat_participants = relationship("ChatParticipant", back_populates="user")
    user_sessions = relationship("UserSession", back_populates="user")

class EmailVerification(Base):
    __tablename__ = "email_verifications"

    verification_id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.user_id"), nullable=False)
    otp_code = Column(String, nullable=False)
    expires_at = Column(DateTime, nullable=False)
    is_used = Column(Boolean, default=False)
    created_at = Column(DateTime, default=func.now())
    verification_type = Column(String, default="email_verification")

    # Relationships
    user = relationship("User", back_populates="email_verifications")

class JwtToken(Base):
    __tablename__ = "jwt_tokens"

    token_id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.user_id"), nullable=False)
    token_hash = Column(String, nullable=False)
    expires_at = Column(DateTime, nullable=False)
    is_revoked = Column(Boolean, default=False)
    created_at = Column(DateTime, default=func.now())
    token_type = Column(String, default="access")

    # Relationships
    user = relationship("User", back_populates="jwt_tokens")

class Chat(Base):
    __tablename__ = "chats"

    chat_id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.user_id"), nullable=False)
    chat_title = Column(String, nullable=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_message_at = Column(DateTime)
    is_archived = Column(Boolean, default=False)
    chat_type = Column(String, default="personal")

    # Relationships
    user = relationship("User", back_populates="chats")
    messages = relationship("Message", back_populates="chat")
    chat_participants = relationship("ChatParticipant", back_populates="chat")

class Message(Base):
    __tablename__ = "messages"

    message_id = Column(Integer, primary_key=True, index=True)
    chat_id = Column(Integer, ForeignKey("chats.chat_id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.user_id"), nullable=False)
    message_content = Column(Text, nullable=False)
    message_type = Column(String, default="text")
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    is_edited = Column(Boolean, default=False)
    edited_at = Column(DateTime)
    sender_type = Column(String, default="user")

    # Relationships
    chat = relationship("Chat", back_populates="messages")
    user = relationship("User", back_populates="messages")
    attachments = relationship("MessageAttachment", back_populates="message")

class MessageAttachment(Base):
    __tablename__ = "message_attachments"

    attachment_id = Column(Integer, primary_key=True, index=True)
    message_id = Column(Integer, ForeignKey("messages.message_id"), nullable=False)
    file_name = Column(String, nullable=False)
    file_path = Column(String, nullable=False)
    file_type = Column(String, nullable=False)
    file_size = Column(Integer, nullable=False)
    mime_type = Column(String, nullable=False)
    uploaded_at = Column(DateTime, default=func.now())
    storage_provider = Column(String, default="local")

    # Relationships
    message = relationship("Message", back_populates="attachments")

class ChatParticipant(Base):
    __tablename__ = "chat_participants"

    participant_id = Column(Integer, primary_key=True, index=True)
    chat_id = Column(Integer, ForeignKey("chats.chat_id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.user_id"), nullable=False)
    role = Column(String, default="member")
    joined_at = Column(DateTime, default=func.now())
    last_read_at = Column(DateTime)
    is_active = Column(Boolean, default=True)

    # Relationships
    chat = relationship("Chat", back_populates="chat_participants")
    user = relationship("User", back_populates="chat_participants")

class UserSession(Base):
    __tablename__ = "user_sessions"

    session_id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.user_id"), nullable=False)
    session_token = Column(String, nullable=False, unique=True)
    ip_address = Column(String)
    user_agent = Column(String)
    created_at = Column(DateTime, default=func.now())
    expires_at = Column(DateTime, nullable=False)
    is_active = Column(Boolean, default=True)

    # Relationships
    user = relationship("User", back_populates="user_sessions")

# Create tables
Base.metadata.create_all(bind=engine)

# Dependency to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Pydantic Models for API
class UserCreate(BaseModel):
    email: EmailStr
    first_name: str
    last_name: str
    password: str

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class EmailVerificationRequest(BaseModel):
    email: EmailStr
    otp_code: str

class Token(BaseModel):
    access_token: str
    token_type: str
    expires_in: int

class UserResponse(BaseModel):
    user_id: int
    email: str
    first_name: str
    last_name: str
    email_verified: bool
    created_at: datetime
    status: str

class ChatCreate(BaseModel):
    chat_title: str
    chat_type: str = "personal"

class ChatResponse(BaseModel):
    chat_id: int
    chat_title: str
    chat_type: str
    created_at: datetime
    last_message_at: Optional[datetime]
    is_archived: bool

class MessageCreate(BaseModel):
    chat_id: int
    message_content: str
    message_type: str = "text"
    sender_type: str = "user"

class MessageResponse(BaseModel):
    message_id: int
    chat_id: int
    user_id: int
    message_content: str
    message_type: str
    created_at: datetime
    sender_type: str
    is_edited: bool

class AttachmentResponse(BaseModel):
    attachment_id: int
    file_name: str
    file_type: str
    file_size: int
    mime_type: str
    uploaded_at: datetime

app = FastAPI(title="Athlix Chat Application", version="2.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# OpenAI client (initialize only if API key is available)
openai_api_key = os.getenv("gpt_api")
client = None
if openai_api_key:
    client = OpenAI(api_key=openai_api_key)
else:
    print("Warning: OpenAI API key not found. AI features will be disabled.")

# Authentication and Utility Functions
def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def generate_otp() -> str:
    return str(secrets.randbelow(900000) + 100000)

def send_verification_email(email: str, otp_code: str) -> bool:
    try:
        if not EMAIL_USER or not EMAIL_PASSWORD:
            print(f"Email verification OTP for {email}: {otp_code}")
            return True

        msg = MIMEMultipart()
        msg['From'] = EMAIL_USER
        msg['To'] = email
        msg['Subject'] = "Athlix - Email Verification"

        body = f"""
        Welcome to Athlix!

        Your email verification code is: {otp_code}

        This code will expire in 10 minutes.

        If you didn't request this verification, please ignore this email.
        """

        msg.attach(MIMEText(body, 'plain'))

        server = smtplib.SMTP(EMAIL_HOST, EMAIL_PORT)
        server.starttls()
        server.login(EMAIL_USER, EMAIL_PASSWORD)
        text = msg.as_string()
        server.sendmail(EMAIL_USER, email, text)
        server.quit()
        return True
    except Exception as e:
        print(f"Failed to send email: {e}")
        return False

def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security), db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: int = payload.get("sub")
        if user_id is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception

    # Check if token is revoked
    token_hash = hashlib.sha256(credentials.credentials.encode()).hexdigest()
    jwt_token = db.query(JwtToken).filter(
        JwtToken.token_hash == token_hash,
        JwtToken.is_revoked == False,
        JwtToken.expires_at > datetime.utcnow()
    ).first()

    if not jwt_token:
        raise credentials_exception

    user = db.query(User).filter(User.user_id == user_id).first()
    if user is None:
        raise credentials_exception
    return user

def create_user_session(user_id: int, ip_address: str, user_agent: str, db: Session):
    session_token = secrets.token_urlsafe(32)
    expires_at = datetime.utcnow() + timedelta(days=30)

    user_session = UserSession(
        user_id=user_id,
        session_token=session_token,
        ip_address=ip_address,
        user_agent=user_agent,
        expires_at=expires_at
    )
    db.add(user_session)
    db.commit()
    return session_token

# Legacy Pydantic models for backward compatibility
class ChatMessage(BaseModel):
    session_id: Optional[str] = None
    message: str
    message_type: str = "text"  # text, image, question_response

class QuestionResponse(BaseModel):
    session_id: str
    question_id: str
    answer: str

class SessionStatus(BaseModel):
    session_id: str
    status: str
    questions: List[Dict] = []
    current_question_index: int = 0
    treatment: Optional[str] = None

class TreatmentResponse(BaseModel):
    session_id: str
    treatment: str
    session_complete: bool

class LegacyChatResponse(BaseModel):
    session_id: str
    message: str
    message_type: str = "ai_response"
    has_question: bool = False
    question: Optional[Dict] = None
    session_complete: bool = False
    treatment: Optional[str] = None

# In-memory storage for legacy sessions (backward compatibility)
sessions: Dict[str, Dict] = {}
chat_sessions: Dict[str, List[Dict]] = {}

# Authentication Routes
@app.post("/auth/signup", response_model=dict)
async def signup(user_data: UserCreate, db: Session = Depends(get_db)):
    """User registration with email verification"""

    # Check if user already exists
    existing_user = db.query(User).filter(User.email == user_data.email).first()
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )

    # Create new user
    hashed_password = get_password_hash(user_data.password)
    new_user = User(
        email=user_data.email,
        first_name=user_data.first_name,
        last_name=user_data.last_name,
        password_hash=hashed_password,
        email_verified=False
    )

    db.add(new_user)
    db.commit()
    db.refresh(new_user)

    # Generate and send OTP
    otp_code = generate_otp()
    expires_at = datetime.utcnow() + timedelta(minutes=10)

    email_verification = EmailVerification(
        user_id=new_user.user_id,
        otp_code=otp_code,
        expires_at=expires_at,
        verification_type="email_verification"
    )

    db.add(email_verification)
    db.commit()

    # Send verification email
    email_sent = send_verification_email(user_data.email, otp_code)

    return {
        "message": "User registered successfully. Please check your email for verification code.",
        "user_id": new_user.user_id,
        "email_sent": email_sent
    }

@app.post("/auth/verify-email", response_model=dict)
async def verify_email(verification_data: EmailVerificationRequest, db: Session = Depends(get_db)):
    """Verify email with OTP code"""

    # Find user by email
    user = db.query(User).filter(User.email == verification_data.email).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Find valid OTP
    verification = db.query(EmailVerification).filter(
        EmailVerification.user_id == user.user_id,
        EmailVerification.otp_code == verification_data.otp_code,
        EmailVerification.is_used == False,
        EmailVerification.expires_at > datetime.utcnow()
    ).first()

    if not verification:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired OTP code"
        )

    # Mark email as verified
    user.email_verified = True
    verification.is_used = True

    db.commit()

    return {"message": "Email verified successfully"}

@app.post("/auth/signin", response_model=Token)
async def signin(login_data: UserLogin, db: Session = Depends(get_db)):
    """User login with JWT token"""

    # Find user by email
    user = db.query(User).filter(User.email == login_data.email).first()
    if not user or not verify_password(login_data.password, user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password"
        )

    if not user.email_verified:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Email not verified. Please verify your email first."
        )

    # Create access token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.user_id)}, expires_delta=access_token_expires
    )

    # Store token in database
    token_hash = hashlib.sha256(access_token.encode()).hexdigest()
    expires_at = datetime.utcnow() + access_token_expires

    jwt_token = JwtToken(
        user_id=user.user_id,
        token_hash=token_hash,
        expires_at=expires_at,
        token_type="access"
    )

    db.add(jwt_token)

    # Update last login
    user.last_login = datetime.utcnow()

    db.commit()

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }

@app.post("/auth/logout")
async def logout(current_user: User = Depends(get_current_user),
                credentials: HTTPAuthorizationCredentials = Depends(security),
                db: Session = Depends(get_db)):
    """Logout user by revoking JWT token"""

    token_hash = hashlib.sha256(credentials.credentials.encode()).hexdigest()
    jwt_token = db.query(JwtToken).filter(JwtToken.token_hash == token_hash).first()

    if jwt_token:
        jwt_token.is_revoked = True
        db.commit()

    return {"message": "Logged out successfully"}

@app.get("/auth/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """Get current user information"""
    return UserResponse(
        user_id=current_user.user_id,
        email=current_user.email,
        first_name=current_user.first_name,
        last_name=current_user.last_name,
        email_verified=current_user.email_verified,
        created_at=current_user.created_at,
        status=current_user.status
    )

# Enhanced Chat Routes
@app.post("/chats/", response_model=ChatResponse)
async def create_chat(chat_data: ChatCreate, current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    """Create a new chat"""

    new_chat = Chat(
        user_id=current_user.user_id,
        chat_title=chat_data.chat_title,
        chat_type=chat_data.chat_type
    )

    db.add(new_chat)
    db.commit()
    db.refresh(new_chat)

    # Add user as participant
    participant = ChatParticipant(
        chat_id=new_chat.chat_id,
        user_id=current_user.user_id,
        role="owner"
    )

    db.add(participant)
    db.commit()

    return ChatResponse(
        chat_id=new_chat.chat_id,
        chat_title=new_chat.chat_title,
        chat_type=new_chat.chat_type,
        created_at=new_chat.created_at,
        last_message_at=new_chat.last_message_at,
        is_archived=new_chat.is_archived
    )

@app.get("/chats/", response_model=List[ChatResponse])
async def get_user_chats(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    """Get all chats for the current user"""

    chats = db.query(Chat).join(ChatParticipant).filter(
        ChatParticipant.user_id == current_user.user_id,
        ChatParticipant.is_active == True,
        Chat.is_archived == False
    ).order_by(Chat.updated_at.desc()).all()

    return [ChatResponse(
        chat_id=chat.chat_id,
        chat_title=chat.chat_title,
        chat_type=chat.chat_type,
        created_at=chat.created_at,
        last_message_at=chat.last_message_at,
        is_archived=chat.is_archived
    ) for chat in chats]

@app.post("/chats/{chat_id}/messages/", response_model=MessageResponse)
async def send_message(
    chat_id: int,
    message_data: MessageCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Send a message to a chat"""

    # Verify user is participant in chat
    participant = db.query(ChatParticipant).filter(
        ChatParticipant.chat_id == chat_id,
        ChatParticipant.user_id == current_user.user_id,
        ChatParticipant.is_active == True
    ).first()

    if not participant:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not a participant in this chat"
        )

    # Create message
    new_message = Message(
        chat_id=chat_id,
        user_id=current_user.user_id,
        message_content=message_data.message_content,
        message_type=message_data.message_type,
        sender_type=message_data.sender_type
    )

    db.add(new_message)

    # Update chat's last message time
    chat = db.query(Chat).filter(Chat.chat_id == chat_id).first()
    if chat:
        chat.last_message_at = datetime.utcnow()
        chat.updated_at = datetime.utcnow()

    db.commit()
    db.refresh(new_message)

    return MessageResponse(
        message_id=new_message.message_id,
        chat_id=new_message.chat_id,
        user_id=new_message.user_id,
        message_content=new_message.message_content,
        message_type=new_message.message_type,
        created_at=new_message.created_at,
        sender_type=new_message.sender_type,
        is_edited=new_message.is_edited
    )

@app.get("/chats/{chat_id}/messages/", response_model=List[MessageResponse])
async def get_chat_messages(
    chat_id: int,
    skip: int = 0,
    limit: int = 50,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get messages from a chat"""

    # Verify user is participant in chat
    participant = db.query(ChatParticipant).filter(
        ChatParticipant.chat_id == chat_id,
        ChatParticipant.user_id == current_user.user_id,
        ChatParticipant.is_active == True
    ).first()

    if not participant:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not a participant in this chat"
        )

    messages = db.query(Message).filter(
        Message.chat_id == chat_id
    ).order_by(Message.created_at.desc()).offset(skip).limit(limit).all()

    return [MessageResponse(
        message_id=message.message_id,
        chat_id=message.chat_id,
        user_id=message.user_id,
        message_content=message.message_content,
        message_type=message.message_type,
        created_at=message.created_at,
        sender_type=message.sender_type,
        is_edited=message.is_edited
    ) for message in reversed(messages)]

@app.post("/chats/{chat_id}/messages/{message_id}/attachments/")
async def upload_message_attachment(
    chat_id: int,
    message_id: int,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Upload an attachment to a message"""

    # Verify message exists and user owns it
    message = db.query(Message).filter(
        Message.message_id == message_id,
        Message.chat_id == chat_id,
        Message.user_id == current_user.user_id
    ).first()

    if not message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Message not found or you don't have permission"
        )

    # Create uploads directory if it doesn't exist
    upload_dir = Path("uploads")
    upload_dir.mkdir(exist_ok=True)

    # Generate unique filename
    file_extension = file.filename.split('.')[-1] if '.' in file.filename else ''
    unique_filename = f"{uuid.uuid4()}.{file_extension}"
    file_path = upload_dir / unique_filename

    # Save file
    with open(file_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)

    # Create attachment record
    attachment = MessageAttachment(
        message_id=message_id,
        file_name=file.filename,
        file_path=str(file_path),
        file_type=file_extension,
        file_size=len(content),
        mime_type=file.content_type or "application/octet-stream"
    )

    db.add(attachment)
    db.commit()
    db.refresh(attachment)

    return AttachmentResponse(
        attachment_id=attachment.attachment_id,
        file_name=attachment.file_name,
        file_type=attachment.file_type,
        file_size=attachment.file_size,
        mime_type=attachment.mime_type,
        uploaded_at=attachment.uploaded_at
    )

def encode_image_from_upload(upload_file: UploadFile) -> str:
    """Convert uploaded file to base64 string"""
    try:
        # Read the file content
        image_content = upload_file.file.read()
        
        # Validate it's an image
        try:
            img = Image.open(io.BytesIO(image_content))
            img.verify()
        except Exception:
            raise HTTPException(status_code=400, detail="Invalid image file")
        
        # Convert to base64
        return base64.b64encode(image_content).decode("utf-8")
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error processing image: {str(e)}")

def get_initial_questions_from_image(base64_image: str) -> List[Dict]:
    """Send image to OpenAI and get initial questions"""
    if not client:
        raise HTTPException(status_code=503, detail="AI service not available. Please configure OpenAI API key.")

    try:
        response = client.chat.completions.create(
            model="gpt-4.1-mini",
            messages=[
                {
                    "role": "system",
                    "content": """You are a medical assessment AI. Analyze the provided image and generate important questions to gather more information for treatment diagnosis. 
                    
                    Return your response as a JSON object with this exact structure:
                    {
                        "questions": [
                            {
                                "id": "q1",
                                "question": "Your question here",
                                "type": "text|multiple_choice|number",
                                "options": ["option1", "option2"] // only for multiple_choice
                            }
                        ]
                    }
                    
                    Generate 3-5 relevant questions based on what you see in the image. Focus on symptoms, pain levels, duration, activities that trigger issues, and medical history relevant to what's shown."""
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "Analyze this image and provide important questions for medical assessment."},
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}
                        }
                    ]
                }
            ],
            max_tokens=1000
        )
        
        # Parse the JSON response
        response_text = response.choices[0].message.content
        questions_data = json.loads(response_text)
        return questions_data.get("questions", [])
        
    except json.JSONDecodeError:
        # Fallback if JSON parsing fails
        return [
            {
                "id": "q1",
                "question": "Can you describe the pain or discomfort you're experiencing?",
                "type": "text"
            },
            {
                "id": "q2", 
                "question": "On a scale of 1-10, how would you rate your pain level?",
                "type": "number"
            },
            {
                "id": "q3",
                "question": "How long have you been experiencing this issue?",
                "type": "multiple_choice",
                "options": ["Less than a week", "1-2 weeks", "1 month", "Several months", "Over a year"]
            }
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting questions from AI: {str(e)}")

def generate_treatment_or_more_questions(session_data: Dict) -> Dict:
    """Generate treatment or ask more questions based on collected data"""
    if not client:
        return {
            "need_more_info": False,
            "treatment": "AI service not available. Please consult with a healthcare professional."
        }

    try:
        # Prepare conversation history
        conversation_context = f"""
        Image Analysis: User uploaded an image for medical assessment.
        
        Questions and Answers:
        """
        
        for qa in session_data.get("qa_history", []):
            conversation_context += f"Q: {qa['question']}\nA: {qa['answer']}\n\n"
        
        response = client.chat.completions.create(
            model="gpt-4",
            messages=[
                {
                    "role": "system",
                    "content": """You are a certified personal trainer and injury prevention expert. Be friendly, clear, and cautious.

Based on the conversation history, determine if you have enough information to provide treatment recommendations.

IMPORTANT: Always provide complete treatment recommendations. Do NOT ask additional questions. 

Provide comprehensive treatment recommendations following this EXACT structure with proper sections:

• Immediate Treatment: [Provide immediate first aid and treatment steps]

• Injury Prevention Tips: [Provide specific prevention strategies]

• Overall Performance Evaluation: [Evaluate how the person handled the situation]

• Suggestions for Improving Performance Safely: [Provide safety improvement suggestions]

• Motivational and Positive Feedback: [Give encouraging and positive feedback]

• Future Workouts Recommendations: [Provide specific workout and recovery recommendations]

Return ONLY a JSON object in this format:
{
    "need_more_info": false,
    "treatment": "Your detailed treatment recommendations here with the exact structure above"
}

Make the treatment recommendations comprehensive, specific, and actionable. Focus on practical advice that the person can implement immediately."""
                },
                {
                    "role": "user",
                    "content": conversation_context
                }
            ],
            max_tokens=2000
        )
        
        response_text = response.choices[0].message.content
        
        # Try to parse as JSON first
        try:
            parsed_response = json.loads(response_text)
            # Ensure we always return treatment, never ask more questions
            if parsed_response.get("need_more_info", False):
                # Force treatment generation if AI tries to ask more questions
                return {
                    "need_more_info": False,
                    "treatment": parsed_response.get("treatment", "Based on the information provided, here are my treatment recommendations...")
                }
            return parsed_response
        except json.JSONDecodeError:
            # If not JSON, assume it's a treatment response
            return {
                "need_more_info": False,
                "treatment": response_text
            }
            
    except Exception as e:
        # Fallback treatment if there's an error
        fallback_treatment = """
        • Immediate Treatment: Clean the affected area with lukewarm water and mild soap. Apply gentle pressure to stop any bleeding and cover with a clean bandage.

        • Injury Prevention Tips: Always wear appropriate safety equipment when engaging in physical activities. Be aware of your surroundings and avoid risky situations when possible.

        • Overall Performance Evaluation: You've done well by seeking medical guidance. Taking prompt action shows good health awareness.

        • Suggestions for Improving Performance Safely: Consider warming up properly before activities and cooling down afterward. Stay hydrated and listen to your body's signals.

        • Motivational and Positive Feedback: You're taking the right steps by getting proper guidance. Your proactive approach to health and safety is commendable.

        • Future Workouts Recommendations: Start with low-impact exercises and gradually increase intensity. Focus on proper form over speed or weight. Consider consulting with a fitness professional for a personalized plan.
        """
        
        return {
            "need_more_info": False,
            "treatment": fallback_treatment
        }

async def generate_chat_response(chat_history: List[Dict], message: str) -> AsyncGenerator[str, None]:
    """Generate streaming chat response for general conversation"""
    if not client:
        yield "I apologize, but the AI service is currently not available. Please configure the OpenAI API key to enable AI features."
        return

    try:
        # Prepare conversation history for OpenAI
        messages = [
            {
                "role": "system",
                "content": """You are an AI Treatment Assistant - a highly knowledgeable medical AI specializing in injury prevention, treatment recommendations, and health guidance.

Key guidelines:
- Be empathetic, supportive, and professional
- Provide specific, actionable medical advice when appropriate
- Ask relevant follow-up questions to understand symptoms better
- Focus on injury prevention, exercise safety, and general wellness
- Encourage users to upload images for better analysis when relevant
- Be conversational and natural in your responses
- Always prioritize user safety and recommend professional medical care when necessary

When users describe symptoms, provide:
1. Immediate care recommendations
2. Questions about location, severity, duration
3. Suggestions for when to seek professional help
4. Prevention tips for the future

Format your responses clearly with:
- Immediate recommendations first
- Follow-up questions
- When to seek professional help
- Prevention strategies

Maintain a caring, professional tone while being highly informative and actionable."""
            }
        ]
        
        # Add chat history
        for msg in chat_history[-10:]:  # Last 10 messages for context
            messages.append({
                "role": msg["role"],
                "content": msg["content"]
            })
        
        # Add current message
        messages.append({
            "role": "user",
            "content": message
        })
        
        # Stream response from OpenAI
        stream = client.chat.completions.create(
            model="gpt-4",
            messages=messages,
            max_tokens=1200,
            temperature=0.7,
            stream=True
        )
        
        for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                yield chunk.choices[0].delta.content
                await asyncio.sleep(0.01)  # Small delay for smooth streaming
                
    except Exception as e:
        yield f"I apologize, but I encountered an error: {str(e)}. Please try describing your symptoms again, and I'll do my best to help you."

def create_chat_session() -> str:
    """Create a new chat session"""
    session_id = str(uuid.uuid4())
    chat_sessions[session_id] = []
    return session_id

def add_message_to_chat(session_id: str, role: str, content: str):
    """Add a message to chat history"""
    if session_id not in chat_sessions:
        chat_sessions[session_id] = []
    
    chat_sessions[session_id].append({
        "role": role,
        "content": content,
        "timestamp": datetime.now().isoformat()
    })

@app.post("/upload-image/")
async def upload_image(file: UploadFile = File(...)):
    """Upload image and get initial questions"""
    
    # Validate file type
    if not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")
    
    # Create session
    session_id = str(uuid.uuid4())
    
    try:
        # Process image
        base64_image = encode_image_from_upload(file)
        
        # Get initial questions
        questions = get_initial_questions_from_image(base64_image)
        
        # Store session data
        sessions[session_id] = {
            "session_id": session_id,
            "image_data": base64_image,
            "questions": questions,
            "current_question_index": 0,
            "qa_history": [],
            "status": "questioning",
            "created_at": datetime.now().isoformat()
        }
        
        return {
            "session_id": session_id,
            "status": "questioning",
            "questions": questions,
            "current_question": questions[0] if questions else None
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing upload: {str(e)}")

@app.post("/answer-question/")
async def answer_question(response: QuestionResponse):
    """Submit answer to a question"""
    
    if response.session_id not in sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    session = sessions[response.session_id]
    
    if session["status"] != "questioning":
        raise HTTPException(status_code=400, detail="Session is not in questioning state")
    
    # Find the question being answered
    current_questions = session["questions"]
    question_found = None
    
    for q in current_questions:
        if q["id"] == response.question_id:
            question_found = q
            break
    
    if not question_found:
        raise HTTPException(status_code=400, detail="Question not found")
    
    # Add to QA history
    session["qa_history"].append({
        "question_id": response.question_id,
        "question": question_found["question"],
        "answer": response.answer
    })
    
    # Move to next question or check if we need treatment
    session["current_question_index"] += 1
    
    if session["current_question_index"] >= len(current_questions):
        # All questions answered, check if we need more info or can provide treatment
        ai_response = generate_treatment_or_more_questions(session)
        
        if ai_response.get("need_more_info", False):
            # Add more questions
            new_questions = ai_response.get("questions", [])
            session["questions"].extend(new_questions)
            session["current_question_index"] = len(current_questions)  # Point to first new question
            
            return {
                "session_id": response.session_id,
                "status": "questioning",
                "current_question": new_questions[0] if new_questions else None,
                "questions_remaining": len(new_questions)
            }
        else:
            # Provide treatment
            treatment = ai_response.get("treatment", "No treatment recommendations available")
            session["treatment"] = treatment
            session["status"] = "completed"
            
            return TreatmentResponse(
                session_id=response.session_id,
                treatment=treatment,
                session_complete=True
            )
    else:
        # More questions in current set
        next_question = current_questions[session["current_question_index"]]
        return {
            "session_id": response.session_id,
            "status": "questioning",
            "current_question": next_question,
            "questions_remaining": len(current_questions) - session["current_question_index"]
        }

@app.post("/chat/")
async def chat_message(chat_msg: ChatMessage):
    """Handle general chat messages"""
    
    # Create session if none provided
    if not chat_msg.session_id:
        chat_msg.session_id = create_chat_session()
    
    # Add user message to chat history
    add_message_to_chat(chat_msg.session_id, "user", chat_msg.message)
    
    return {"session_id": chat_msg.session_id, "message": "Message received"}

@app.get("/chat/{session_id}/stream")
async def stream_chat_response(session_id: str, message: str):
    """Stream AI response for chat"""
    
    if session_id not in chat_sessions:
        raise HTTPException(status_code=404, detail="Chat session not found")
    
    async def generate():
        chat_history = chat_sessions[session_id]
        full_response = ""
        
        async for chunk in generate_chat_response(chat_history, message):
            full_response += chunk
            yield f"data: {json.dumps({'chunk': chunk, 'done': False})}\n\n"
        
        # Add AI response to chat history
        add_message_to_chat(session_id, "assistant", full_response)
        
        # Send completion signal
        yield f"data: {json.dumps({'chunk': '', 'done': True})}\n\n"
    
    return StreamingResponse(
        generate(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/plain",
        }
    )

@app.post("/chat/{session_id}/message")
async def send_chat_message(session_id: str, message: str):
    """Send a message and get immediate response (non-streaming)"""
    
    if session_id not in chat_sessions:
        chat_sessions[session_id] = []
    
    # Add user message
    add_message_to_chat(session_id, "user", message)
    
    # Generate AI response
    chat_history = chat_sessions[session_id]
    full_response = ""
    
    async for chunk in generate_chat_response(chat_history, message):
        full_response += chunk
    
    # Add AI response to chat history
    add_message_to_chat(session_id, "assistant", full_response)
    
    return {
        "session_id": session_id,
        "response": full_response,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/session/{session_id}")
async def get_session_status(session_id: str):
    """Get current session status"""
    
    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    session = sessions[session_id]
    
    return SessionStatus(
        session_id=session_id,
        status=session["status"],
        questions=session["questions"],
        current_question_index=session["current_question_index"],
        treatment=session.get("treatment")
    )

@app.get("/session/{session_id}/treatment")
async def get_treatment(session_id: str):
    """Get treatment recommendations for completed session"""
    
    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    session = sessions[session_id]
    
    if session["status"] != "completed":
        raise HTTPException(status_code=400, detail="Session not completed yet")
    
    return {
        "session_id": session_id,
        "treatment": session.get("treatment", "No treatment available"),
        "qa_history": session.get("qa_history", [])
    }

@app.get("/chat/{session_id}/history")
async def get_chat_history(session_id: str):
    """Get chat history for a session"""
    
    if session_id not in chat_sessions:
        return {"session_id": session_id, "messages": []}
    
    return {
        "session_id": session_id,
        "messages": chat_sessions[session_id]
    }

@app.delete("/session/{session_id}")
async def delete_session(session_id: str):
    """Delete a session"""
    
    if session_id in sessions:
        del sessions[session_id]
    
    if session_id in chat_sessions:
        del chat_sessions[session_id]
    
    return {"message": "Session deleted successfully"}

@app.post("/chat/new")
async def create_new_chat():
    """Create a new chat session"""
    session_id = create_chat_session()
    return {"session_id": session_id}

@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "AI Treatment Assistant API", "status": "active", "version": "2.0.0"}

@app.get("/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "active_sessions": len(sessions),
        "active_chats": len(chat_sessions),
        "timestamp": datetime.now().isoformat()
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)