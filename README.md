# Athlix Chat Application

A comprehensive FastAPI-based chat application with user authentication, email verification, JWT tokens, and AI-powered features.

## Features

### 🔐 Authentication & Security
- User registration with email verification via OTP
- JWT token-based authentication
- Password hashing with bcrypt
- Session management with IP tracking
- Token revocation and logout functionality

### 💬 Chat System
- Create and manage multiple chats
- Send and receive messages in real-time
- Message attachments support
- Chat participants management
- Persistent message history

### 🤖 AI Integration
- OpenAI GPT integration for intelligent responses
- Image analysis for medical assessments
- Treatment recommendations
- Interactive Q&A sessions

### 📊 Database
- SQLAlchemy ORM with SQLite
- Complete ERD implementation
- Database migrations support
- Comprehensive data models

## Quick Start

### 1. <PERSON>lone and Setup

```bash
git clone <repository-url>
cd athlix
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Environment Configuration

```bash
# Copy the template and configure your settings
cp .env.template .env

# Edit .env with your configuration
# At minimum, set:
# - SECRET_KEY (generate a secure random key)
# - gpt_api (your OpenAI API key)
# - EMAIL_* settings (for email verification)
```

### 4. Initialize Database

```bash
python init_db.py
```

### 5. Run the Application

```bash
# Development mode
uvicorn main:app --reload

# Production mode
python main.py
```

### 6. Access the Application

- **API Documentation**: http://localhost:8000/docs
- **Alternative Docs**: http://localhost:8000/redoc
- **Health Check**: http://localhost:8000/health

## API Endpoints

### Authentication
- `POST /auth/signup` - User registration
- `POST /auth/signin` - User login
- `POST /auth/verify-email` - Email verification
- `POST /auth/logout` - User logout
- `GET /auth/me` - Get current user info

### Chat Management
- `POST /chats/` - Create new chat
- `GET /chats/` - Get user's chats
- `POST /chats/{chat_id}/messages/` - Send message
- `GET /chats/{chat_id}/messages/` - Get chat messages
- `POST /chats/{chat_id}/messages/{message_id}/attachments/` - Upload attachment

### Legacy AI Features (Backward Compatible)
- `POST /upload-image/` - Upload image for analysis
- `POST /answer-question/` - Answer assessment questions
- `POST /chat/` - General chat messages
- `GET /chat/{session_id}/stream` - Streaming chat responses

## Database Schema

The application implements a complete ERD with the following entities:

- **Users**: User accounts and profiles
- **EmailVerifications**: OTP-based email verification
- **JwtTokens**: JWT token management
- **Chats**: Chat rooms and conversations
- **Messages**: Individual messages
- **MessageAttachments**: File attachments
- **ChatParticipants**: Chat membership
- **UserSessions**: Session tracking

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | Database connection string | `sqlite:///./athlix.db` |
| `SECRET_KEY` | JWT signing key | Required |
| `ACCESS_TOKEN_EXPIRE_MINUTES` | Token expiration time | `30` |
| `EMAIL_HOST` | SMTP server host | `smtp.gmail.com` |
| `EMAIL_PORT` | SMTP server port | `587` |
| `EMAIL_USER` | SMTP username | Required for email |
| `EMAIL_PASSWORD` | SMTP password | Required for email |
| `gpt_api` | OpenAI API key | Required for AI features |

### Email Configuration

For Gmail, use an App Password:
1. Enable 2-factor authentication
2. Generate an App Password
3. Use the App Password as `EMAIL_PASSWORD`

## Development

### Project Structure

```
athlix/
├── main.py              # Main application file
├── init_db.py          # Database initialization
├── requirements.txt    # Python dependencies
├── .env.template      # Environment template
├── README.md          # This file
├── uploads/           # File uploads directory
└── athlix.db         # SQLite database (created automatically)
```

### Adding New Features

1. **Database Models**: Add to `main.py` in the models section
2. **API Routes**: Add new endpoints following the existing pattern
3. **Authentication**: Use `Depends(get_current_user)` for protected routes
4. **Database**: Use `Depends(get_db)` for database access

### Testing

```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run tests (when test files are created)
pytest
```

## Production Deployment

### Security Checklist
- [ ] Change `SECRET_KEY` to a secure random value
- [ ] Use a production database (PostgreSQL recommended)
- [ ] Configure proper CORS origins
- [ ] Set up HTTPS/SSL
- [ ] Configure email service
- [ ] Set up monitoring and logging

### Docker Deployment (Optional)

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
RUN python init_db.py

EXPOSE 8000
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check `DATABASE_URL` in `.env`
   - Run `python init_db.py` to initialize

2. **Email Verification Not Working**
   - Check email configuration in `.env`
   - For development, OTP codes are printed to console

3. **JWT Token Issues**
   - Ensure `SECRET_KEY` is set and consistent
   - Check token expiration settings

4. **OpenAI API Errors**
   - Verify `gpt_api` key is valid
   - Check API quota and billing

## Support

For issues and questions:
1. Check the API documentation at `/docs`
2. Review the troubleshooting section
3. Check application logs for error details

## License

This project is licensed under the MIT License.
