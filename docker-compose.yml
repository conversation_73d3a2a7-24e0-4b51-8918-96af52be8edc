version: '3.8'

services:
  athlix-app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./athlix.db
      - SECRET_KEY=your-super-secret-key-change-this-in-production
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
    volumes:
      - ./uploads:/app/uploads
      - ./athlix.db:/app/athlix.db
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add PostgreSQL for production
  # postgres:
  #   image: postgres:15
  #   environment:
  #     POSTGRES_DB: athlix
  #     POSTGRES_USER: athlix
  #     POSTGRES_PASSWORD: your-password
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   ports:
  #     - "5432:5432"

# volumes:
#   postgres_data:
